import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { CardComponent } from '../../../shared/components/card/card.component';
import { ButtonComponent } from '../../../shared/components/button/button.component';
import { ApplicationsService } from '../applications.service';
import { Application } from '../../../shared/models/application.model';

@Component({
  selector: 'app-application-detail',
  standalone: true,
  imports: [CommonModule, RouterModule, CardComponent, ButtonComponent],
  template: `
    <div class="application-detail-page">
      <!-- Loading State -->
      <div *ngIf="isLoading" class="loading-state">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <p>Loading application details...</p>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="hasError && !isLoading" class="error-state">
        <div class="error-content">
          <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
          <h3>Failed to load application</h3>
          <p>There was an error loading the application details. Please try again.</p>
          <app-button variant="primary" (click)="loadApplication()">
            Retry
          </app-button>
        </div>
      </div>

      <!-- Application Content -->
      <div *ngIf="!isLoading && !hasError && application" class="application-content">
        <div class="page-header">
          <div class="header-content">
            <div class="header-info">
              <h1 class="page-title">{{ application.name }}</h1>
              <p class="page-subtitle">{{ application.description }}</p>
              <div class="header-meta">
                <span class="app-status" [class]="'status-' + application.status.toLowerCase()">
                  {{ application.status }}
                </span>
                <span class="app-criticality" [class]="'criticality-' + application.criticality.toLowerCase()">
                  {{ application.criticality }}
                </span>
                <span class="app-department">{{ application.department }}</span>
              </div>
            </div>
            <div class="header-actions">
              <app-button variant="outline" routerLink="/applications">
                Back to List
              </app-button>
              <app-button variant="primary" [routerLink]="['/applications', application.id, 'edit']">
                Edit Application
              </app-button>
            </div>
          </div>
        </div>

      <div class="detail-content">
        <div class="detail-grid">
          <!-- Basic Information -->
          <app-card title="Basic Information" class="info-card">
            <div class="info-grid">
              <div class="info-item">
                <label>Owner</label>
                <span>{{ application.owner }}</span>
              </div>
              <div class="info-item">
                <label>Department</label>
                <span>{{ application.department }}</span>
              </div>
              <div class="info-item">
                <label>Version</label>
                <span>{{ application.version }}</span>
              </div>
              <div class="info-item" *ngIf="application.repository">
                <label>Repository</label>
                <a [href]="application.repository" target="_blank" class="repo-link">{{ application.repository }}</a>
              </div>
              <div class="info-item" *ngIf="application.url">
                <label>Deployment URL</label>
                <a [href]="application.url" target="_blank" class="deployment-link">{{ application.url }}</a>
              </div>
              <div class="info-item" *ngIf="application.documentation">
                <label>Documentation</label>
                <a [href]="application.documentation" target="_blank" class="doc-link">{{ application.documentation }}</a>
              </div>
              <div class="info-item">
                <label>Created</label>
                <span>{{ application.createdDate | date:'medium' }}</span>
              </div>
              <div class="info-item">
                <label>Last Updated</label>
                <span>{{ application.lastUpdated | date:'medium' }}</span>
              </div>
            </div>
          </app-card>

          <!-- Quick Stats -->
          <app-card title="Quick Stats" class="stats-card">
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ application.dependencies?.length || 0 }}</div>
                <div class="stat-label">Dependencies</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.techStack?.length || 0 }}</div>
                <div class="stat-label">Tech Stack Items</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.stakeholders?.length || 0 }}</div>
                <div class="stat-label">Stakeholders</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.documents?.length || 0 }}</div>
                <div class="stat-label">Documents</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.vulnerabilities?.length || 0 }}</div>
                <div class="stat-label">Vulnerabilities</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.securityAssessments?.length || 0 }}</div>
                <div class="stat-label">Security Assessments</div>
              </div>
            </div>
          </app-card>

          <!-- Performance Scores -->
          <app-card title="Performance Scores" class="scores-card">
            <div class="scores-grid">
              <div class="score-item">
                <div class="score-circle">
                  <div class="score-value" [class]="getScoreClass(application.healthScore)">{{ application.healthScore }}%</div>
                  <div class="score-label">Health</div>
                </div>
              </div>
              <div class="score-item">
                <div class="score-circle">
                  <div class="score-value" [class]="getScoreClass(application.securityScore)">{{ application.securityScore }}%</div>
                  <div class="score-label">Security</div>
                </div>
              </div>
              <div class="score-item">
                <div class="score-circle">
                  <div class="score-value" [class]="getScoreClass(application.performanceScore)">{{ application.performanceScore }}%</div>
                  <div class="score-label">Performance</div>
                </div>
              </div>
            </div>
          </app-card>

          <!-- Security Overview -->
          <app-card title="Security Overview" class="security-card">
            <div class="security-content">
              <div class="vulnerability-summary">
                <div class="vuln-item">
                  <span class="vuln-count critical">{{ getVulnerabilityCount('critical') }}</span>
                  <span class="vuln-label">Critical</span>
                </div>
                <div class="vuln-item">
                  <span class="vuln-count high">{{ getVulnerabilityCount('high') }}</span>
                  <span class="vuln-label">High</span>
                </div>
                <div class="vuln-item">
                  <span class="vuln-count medium">{{ getVulnerabilityCount('medium') }}</span>
                  <span class="vuln-label">Medium</span>
                </div>
                <div class="vuln-item">
                  <span class="vuln-count low">{{ getVulnerabilityCount('low') }}</span>
                  <span class="vuln-label">Low</span>
                </div>
              </div>
            </div>
          </app-card>

          <!-- Dependencies -->
          <app-card title="Dependencies" class="dependencies-card" *ngIf="application.dependencies && application.dependencies.length > 0">
            <div class="dependencies-list">
              <div class="dependency-item" *ngFor="let dep of application.dependencies">
                <div class="dependency-header">
                  <h4 class="dependency-name">{{ dep.name }}</h4>
                  <span class="dependency-type">{{ dep.type }}</span>
                  <span class="dependency-criticality" [class]="'criticality-' + dep.criticality">{{ dep.criticality }}</span>
                </div>
                <div class="dependency-details">
                  <span class="dependency-version">v{{ dep.version }}</span>
                  <span class="dependency-status" [class]="'status-' + dep.status">{{ dep.status }}</span>
                  <span class="dependency-internal" *ngIf="dep.isInternal">Internal</span>
                </div>
                <p class="dependency-description" *ngIf="dep.description">{{ dep.description }}</p>
              </div>
            </div>
          </app-card>

          <!-- Tech Stack -->
          <app-card title="Technology Stack" class="techstack-card" *ngIf="application.techStack && application.techStack.length > 0">
            <div class="techstack-list">
              <div class="techstack-item" *ngFor="let tech of application.techStack">
                <div class="techstack-header">
                  <h4 class="techstack-name">{{ tech.name }}</h4>
                  <span class="techstack-category">{{ tech.category }}</span>
                </div>
                <div class="techstack-details">
                  <span class="techstack-version">v{{ tech.version }}</span>
                </div>
              </div>
            </div>
          </app-card>

          <!-- Stakeholders -->
          <app-card title="Stakeholders" class="stakeholders-card" *ngIf="application.stakeholders && application.stakeholders.length > 0">
            <div class="stakeholders-list">
              <div class="stakeholder-item" *ngFor="let stakeholder of application.stakeholders">
                <div class="stakeholder-header">
                  <h4 class="stakeholder-name">{{ stakeholder.name }}</h4>
                  <span class="stakeholder-role">{{ stakeholder.role }}</span>
                  <span class="stakeholder-primary" *ngIf="stakeholder.isPrimary">Primary</span>
                </div>
                <div class="stakeholder-details">
                  <span class="stakeholder-email">{{ stakeholder.email }}</span>
                  <span class="stakeholder-department">{{ stakeholder.department }}</span>
                </div>
                <p class="stakeholder-responsibility" *ngIf="stakeholder.responsibility">{{ stakeholder.responsibility }}</p>
              </div>
            </div>
          </app-card>

          <!-- Documents -->
          <app-card title="Documentation" class="documents-card" *ngIf="application.documents && application.documents.length > 0">
            <div class="documents-list">
              <div class="document-item" *ngFor="let doc of application.documents">
                <div class="document-header">
                  <h4 class="document-title">{{ doc.title }}</h4>
                  <span class="document-type">{{ doc.type }}</span>
                  <span class="document-version">v{{ doc.version }}</span>
                </div>
                <p class="document-description" *ngIf="doc.description">{{ doc.description }}</p>
                <div class="document-meta">
                  <span class="document-uploaded" *ngIf="doc.uploadedAt">Uploaded {{ doc.uploadedAt | date:'short' }}</span>
                  <span class="document-size" *ngIf="doc.fileSize">{{ doc.fileSize | number }} bytes</span>
                </div>
              </div>
            </div>
          </app-card>

          <!-- Vulnerabilities -->
          <app-card title="Vulnerabilities" class="vulnerabilities-card" *ngIf="application.vulnerabilities && application.vulnerabilities.length > 0">
            <div class="vulnerabilities-list">
              <div class="vulnerability-item" *ngFor="let vuln of application.vulnerabilities">
                <div class="vulnerability-header">
                  <h4 class="vulnerability-title">{{ vuln.title }}</h4>
                  <span class="vulnerability-severity" [class]="'severity-' + vuln.severity">{{ vuln.severity }}</span>
                  <span class="vulnerability-status" [class]="'status-' + vuln.status">{{ vuln.status }}</span>
                </div>
                <p class="vulnerability-description">{{ vuln.description }}</p>
                <div class="vulnerability-meta">
                  <span class="vulnerability-cve" *ngIf="vuln.cveId">{{ vuln.cveId }}</span>
                  <span class="vulnerability-score" *ngIf="vuln.cvssScore">CVSS: {{ vuln.cvssScore }}</span>
                  <span class="vulnerability-discovered">Discovered {{ vuln.discoveredAt | date:'short' }}</span>
                </div>
              </div>
            </div>
          </app-card>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .application-detail-page {
      min-height: 100%;
    }

    /* Loading State */
    .loading-state {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
      padding: var(--spacing-xl);
    }

    .loading-content {
      text-align: center;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid var(--secondary-200);
      border-top: 3px solid var(--primary-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto var(--spacing-md);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-content p {
      margin: 0;
      color: var(--secondary-600);
      font-size: var(--text-sm);
    }

    /* Error State */
    .error-state {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
      padding: var(--spacing-xl);
    }

    .error-content {
      text-align: center;
      max-width: 400px;
    }

    .error-icon {
      width: 64px;
      height: 64px;
      color: var(--error-500);
      margin: 0 auto var(--spacing-lg);
    }

    .error-content h3 {
      margin-bottom: var(--spacing-sm);
      color: var(--secondary-900);
    }

    .error-content p {
      margin-bottom: var(--spacing-lg);
      color: var(--secondary-600);
    }

    .page-header {
      margin-bottom: var(--spacing-xl);
    }

    .header-content {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      gap: var(--spacing-lg);
    }

    .header-info {
      flex: 1;
    }

    .page-title {
      margin: 0 0 var(--spacing-xs) 0;
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);
    }

    .page-subtitle {
      margin: 0 0 var(--spacing-md) 0;
      font-size: var(--text-base);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .header-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      flex-wrap: wrap;
    }

    .app-status {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
      text-transform: uppercase;

      &.status-production {
        background: var(--success-100);
        color: var(--success-700);
      }

      &.status-development {
        background: var(--primary-100);
        color: var(--primary-700);
      }

      &.status-testing {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.status-deprecated {
        background: var(--error-100);
        color: var(--error-700);
      }
    }

    .app-criticality {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;

      &.criticality-critical {
        background: var(--error-100);
        color: var(--error-700);
      }

      &.criticality-high {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.criticality-medium {
        background: var(--primary-100);
        color: var(--primary-700);
      }

      &.criticality-low {
        background: var(--success-100);
        color: var(--success-700);
      }
    }

    .app-department {
      color: var(--secondary-500);
      font-size: var(--text-sm);
      font-weight: 500;
    }

    .header-actions {
      display: flex;
      gap: var(--spacing-sm);
      flex-shrink: 0;
    }

    .detail-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--spacing-lg);
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-md);
      padding: var(--spacing-lg);
    }

    .info-item {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
    }

    .info-item label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-600);
    }

    .info-item span {
      font-size: var(--text-sm);
      color: var(--secondary-900);
    }

    .status-badge, .criticality-badge {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
      text-transform: uppercase;
      width: fit-content;
    }

    .status-production {
      background: var(--success-100);
      color: var(--success-700);
    }

    .criticality-critical {
      background: var(--error-100);
      color: var(--error-700);
    }

    .repo-link, .deployment-link {
      color: var(--primary-600);
      text-decoration: none;
      font-size: var(--text-sm);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: var(--spacing-lg);
      padding: var(--spacing-lg);
    }

    .stat-item {
      text-align: center;
    }

    .stat-value {
      font-size: var(--text-2xl);
      font-weight: 700;
      color: var(--primary-600);
      margin-bottom: var(--spacing-xs);
    }

    .stat-label {
      font-size: var(--text-sm);
      color: var(--secondary-600);
    }

    /* Performance Scores */
    .scores-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: var(--spacing-lg);
      padding: var(--spacing-lg);
    }

    .score-item {
      text-align: center;
    }

    .score-circle {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-sm);
    }

    .score-circle .score-value {
      font-size: var(--text-2xl);
      font-weight: 700;
      padding: var(--spacing-md);
      border-radius: 50%;
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 3px solid;

      &.score-excellent {
        color: var(--success-700);
        border-color: var(--success-500);
        background: var(--success-50);
      }

      &.score-good {
        color: var(--primary-700);
        border-color: var(--primary-500);
        background: var(--primary-50);
      }

      &.score-fair {
        color: var(--warning-700);
        border-color: var(--warning-500);
        background: var(--warning-50);
      }

      &.score-poor {
        color: var(--error-700);
        border-color: var(--error-500);
        background: var(--error-50);
      }
    }

    .score-circle .score-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .security-content {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
      padding: var(--spacing-lg);
    }

    .security-score {
      text-align: center;
    }

    .score-value {
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--success-600);
      margin-bottom: var(--spacing-xs);
    }

    .score-label {
      font-size: var(--text-sm);
      color: var(--secondary-600);
    }

    .vulnerability-summary {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: var(--spacing-md);
    }

    .vuln-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }

    .vuln-count {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--text-xs);
      font-weight: 600;
      color: white;

      &.critical { background: var(--error-500); }
      &.high { background: var(--warning-500); }
      &.medium { background: var(--primary-500); }
      &.low { background: var(--success-500); }
    }

    .vuln-label {
      font-size: var(--text-sm);
      color: var(--secondary-700);
    }

    /* Dependencies */
    .dependencies-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .dependency-item {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      background: white;
    }

    .dependency-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .dependency-name {
      margin: 0;
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .dependency-type {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--neutral-100);
      color: var(--secondary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .dependency-criticality {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;

      &.criticality-critical {
        background: var(--error-100);
        color: var(--error-700);
      }

      &.criticality-high {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.criticality-medium {
        background: var(--primary-100);
        color: var(--primary-700);
      }

      &.criticality-low {
        background: var(--success-100);
        color: var(--success-700);
      }
    }

    .dependency-details {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .dependency-version {
      font-size: var(--text-sm);
      color: var(--secondary-600);
      font-weight: 500;
    }

    .dependency-status {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;

      &.status-active {
        background: var(--success-100);
        color: var(--success-700);
      }

      &.status-deprecated {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.status-end_of_life {
        background: var(--error-100);
        color: var(--error-700);
      }
    }

    .dependency-internal {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--primary-100);
      color: var(--primary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .dependency-description {
      margin: 0;
      font-size: var(--text-sm);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    /* Tech Stack */
    .techstack-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: var(--spacing-md);
    }

    .techstack-item {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      background: white;
    }

    .techstack-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-sm);
    }

    .techstack-name {
      margin: 0;
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .techstack-category {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--neutral-100);
      color: var(--secondary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .techstack-version {
      font-size: var(--text-sm);
      color: var(--secondary-600);
      font-weight: 500;
    }

    /* Stakeholders */
    .stakeholders-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .stakeholder-item {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      background: white;
    }

    .stakeholder-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .stakeholder-name {
      margin: 0;
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .stakeholder-role {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--primary-100);
      color: var(--primary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .stakeholder-primary {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--success-100);
      color: var(--success-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .stakeholder-details {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .stakeholder-email {
      font-size: var(--text-sm);
      color: var(--secondary-600);
    }

    .stakeholder-department {
      font-size: var(--text-sm);
      color: var(--secondary-500);
    }

    .stakeholder-responsibility {
      margin: 0;
      font-size: var(--text-sm);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    /* Documents */
    .documents-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .document-item {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      background: white;
    }

    .document-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .document-title {
      margin: 0;
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .document-type {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--neutral-100);
      color: var(--secondary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .document-version {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--primary-100);
      color: var(--primary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .document-description {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--text-sm);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .document-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      flex-wrap: wrap;
    }

    .document-uploaded,
    .document-size {
      font-size: var(--text-xs);
      color: var(--secondary-500);
    }

    /* Vulnerabilities */
    .vulnerabilities-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .vulnerability-item {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      background: white;
    }

    .vulnerability-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .vulnerability-title {
      margin: 0;
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .vulnerability-severity {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;

      &.severity-critical {
        background: var(--error-100);
        color: var(--error-700);
      }

      &.severity-high {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.severity-medium {
        background: var(--primary-100);
        color: var(--primary-700);
      }

      &.severity-low {
        background: var(--success-100);
        color: var(--success-700);
      }
    }

    .vulnerability-status {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;

      &.status-open {
        background: var(--error-100);
        color: var(--error-700);
      }

      &.status-in_progress {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.status-resolved {
        background: var(--success-100);
        color: var(--success-700);
      }
    }

    .vulnerability-description {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--text-sm);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .vulnerability-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      flex-wrap: wrap;
    }

    .vulnerability-cve,
    .vulnerability-score,
    .vulnerability-discovered {
      font-size: var(--text-xs);
      color: var(--secondary-500);
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
      }

      .header-actions {
        justify-content: flex-end;
      }

      .detail-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .info-grid {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .vulnerability-summary {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ApplicationDetailComponent implements OnInit, OnDestroy {
  application: Application | null = null;
  isLoading = true;
  hasError = false;
  applicationId: number | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private applicationsService: ApplicationsService
  ) {}

  ngOnInit(): void {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.applicationId = +params['id'];
      if (this.applicationId) {
        this.loadApplication();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadApplication(): void {
    if (!this.applicationId) return;

    this.isLoading = true;
    this.hasError = false;

    this.applicationsService.getApplication(this.applicationId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (app) => {
          this.application = app;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading application:', error);
          this.hasError = true;
          this.isLoading = false;
        }
      });
  }

  getScoreClass(score: number): string {
    if (score >= 90) return 'score-excellent';
    if (score >= 80) return 'score-good';
    if (score >= 70) return 'score-fair';
    return 'score-poor';
  }

  getVulnerabilityCount(severity: string): number {
    if (!this.application?.vulnerabilities) return 0;
    return this.application.vulnerabilities.filter(v => v.severity.toLowerCase() === severity.toLowerCase()).length;
  }
}
