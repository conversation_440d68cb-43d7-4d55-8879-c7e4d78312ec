import { Component, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';
import { CardComponent } from '../../../shared/components/card/card.component';
import { ButtonComponent } from '../../../shared/components/button/button.component';
import { FilterConfig, SearchFilterState } from '../../../shared/components/search-filter/search-filter.component';
import { ApplicationsService, ApplicationsResponse, ApplicationsFilter } from '../applications.service';
import { Application, ApplicationStatus, ApplicationCriticality } from '../../../shared/models/application.model';

@Component({
  selector: 'app-applications-list',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule, CardComponent, ButtonComponent],
  template: `
    <div class="applications-page">
      <div class="page-header">
        <div class="header-content">
          <div class="header-info">
            <h1 class="page-title">Applications</h1>
            <p class="page-subtitle">
              Manage and monitor all your applications in one place.
            </p>
          </div>
          <div class="header-actions">
            <app-button
              variant="primary"
              leftIcon="M12 4v16m8-8H4"
              routerLink="/applications/new"
            >
              Add Application
            </app-button>
          </div>
        </div>
      </div>

      <div class="applications-content">
        <!-- Search and Controls -->
        <div class="controls-section">
          <div class="search-controls">
            <div class="search-input">
              <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
              <input
                type="text"
                placeholder="Search applications..."
                [(ngModel)]="searchFilterState.search"
                (input)="onSearchFilterChange(searchFilterState)"
                class="search-field"
              >
            </div>
            <div class="control-buttons">
              <app-button
                variant="outline"
                leftIcon="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"
                (click)="toggleFiltersOverlay()"
                [class.active]="showFiltersOverlay"
              >
                Filters
                <span class="filter-count" *ngIf="getActiveFilterCount() > 0">{{ getActiveFilterCount() }}</span>
              </app-button>
              <div class="view-toggle">
                <app-button
                  variant="ghost"
                  size="sm"
                  [class.active]="viewMode === 'card'"
                  (click)="viewMode = 'card'"
                  leftIcon="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                >
                  Cards
                </app-button>
                <app-button
                  variant="ghost"
                  size="sm"
                  [class.active]="viewMode === 'grid'"
                  (click)="viewMode = 'grid'"
                  leftIcon="M3 4h18v2H3V4zm0 7h18v2H3v-2zm0 7h18v2H3v-2z"
                >
                  Grid
                </app-button>
              </div>
            </div>
          </div>

          <!-- Filters Overlay -->
          <div class="filters-overlay" [class.show]="showFiltersOverlay" *ngIf="filterConfigs.length > 0">
            <div class="filters-content">
              <div class="filters-header">
                <h3>Filter Applications</h3>
                <button class="close-filters" (click)="closeFiltersOverlay()">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
              <div class="filters-grid">
                <div class="filter-group" *ngFor="let config of filterConfigs">
                  <label class="filter-label">{{ config.label }}</label>
                  <div *ngIf="config.type === 'multiselect'" class="multiselect">
                    <div class="multiselect-options">
                      <label class="checkbox-option" *ngFor="let option of config.options">
                        <input
                          type="checkbox"
                          [value]="option.value"
                          [checked]="isOptionSelected(config.key, option.value)"
                          (change)="onMultiSelectChange(config.key, option.value, $event)"
                        >
                        <span class="checkbox-label">{{ option.label }}</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="filters-actions">
                <app-button variant="ghost" size="sm" (click)="clearFilters()">
                  Clear All
                </app-button>
                <app-button variant="primary" size="sm" (click)="closeFiltersOverlay()">
                  Apply Filters
                </app-button>
              </div>
            </div>
          </div>
        </div>

        <!-- Applications List -->
        <app-card
          [title]="'Applications List'"
          [subtitle]="getSubtitle()"
          class="applications-card"
        >
          <!-- Loading State -->
          <div *ngIf="isLoading" class="loading-state">
            <div class="loading-grid">
              <div class="loading-item" *ngFor="let item of [1,2,3,4,5,6]"></div>
            </div>
          </div>

          <!-- Error State -->
          <div *ngIf="hasError && !isLoading" class="error-state">
            <div class="error-content">
              <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
              <h3>Failed to load applications</h3>
              <p>There was an error loading the applications list. Please try again.</p>
              <app-button variant="primary" (click)="loadApplications()">
                Retry
              </app-button>
            </div>
          </div>

          <!-- Applications Card View -->
          <div *ngIf="!isLoading && !hasError && viewMode === 'card'" class="applications-grid">
            <div class="application-item" *ngFor="let app of applicationsResponse?.applications">
              <div class="app-header">
                <h3 class="app-name">{{ app.name }}</h3>
                <span class="app-status" [class]="'status-' + app.status.toLowerCase()">
                  {{ app.status }}
                </span>
              </div>
              <p class="app-description">{{ app.description }}</p>
              <div class="app-meta">
                <span class="app-owner">{{ app.owner }}</span>
                <span class="app-department">{{ app.department }}</span>
                <span class="app-criticality" [class]="'criticality-' + app.criticality.toLowerCase()">
                  {{ app.criticality }}
                </span>
              </div>
              <div class="app-scores">
                <div class="score-item">
                  <span class="score-label">Health</span>
                  <span class="score-value" [class]="getScoreClass(app.healthScore)">{{ app.healthScore }}%</span>
                </div>
                <div class="score-item">
                  <span class="score-label">Security</span>
                  <span class="score-value" [class]="getScoreClass(app.securityScore)">{{ app.securityScore }}%</span>
                </div>
              </div>
              <div class="app-actions">
                <app-button variant="ghost" size="sm" [routerLink]="['/applications', app.id]">
                  View Details
                </app-button>
                <app-button variant="outline" size="sm" [routerLink]="['/applications', app.id, 'edit']">
                  Edit
                </app-button>
              </div>
            </div>
          </div>

          <!-- Applications Grid View -->
          <div *ngIf="!isLoading && !hasError && viewMode === 'grid'" class="applications-table">
            <table class="table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Owner</th>
                  <th>Department</th>
                  <th>Status</th>
                  <th>Criticality</th>
                  <th>Health</th>
                  <th>Security</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let app of applicationsResponse?.applications" class="table-row">
                  <td class="app-name-cell">
                    <div class="app-name-content">
                      <h4 class="app-name">{{ app.name }}</h4>
                      <p class="app-description-short">{{ app.description | slice:0:80 }}{{ app.description.length > 80 ? '...' : '' }}</p>
                    </div>
                  </td>
                  <td>{{ app.owner }}</td>
                  <td>{{ app.department }}</td>
                  <td>
                    <span class="app-status" [class]="'status-' + app.status.toLowerCase()">
                      {{ app.status }}
                    </span>
                  </td>
                  <td>
                    <span class="app-criticality" [class]="'criticality-' + app.criticality.toLowerCase()">
                      {{ app.criticality }}
                    </span>
                  </td>
                  <td>
                    <span class="score-value" [class]="getScoreClass(app.healthScore)">{{ app.healthScore }}%</span>
                  </td>
                  <td>
                    <span class="score-value" [class]="getScoreClass(app.securityScore)">{{ app.securityScore }}%</span>
                  </td>
                  <td class="actions-cell">
                    <div class="table-actions">
                      <app-button variant="ghost" size="sm" [routerLink]="['/applications', app.id]">
                        View
                      </app-button>
                      <app-button variant="outline" size="sm" [routerLink]="['/applications', app.id, 'edit']">
                        Edit
                      </app-button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Empty State -->
          <div *ngIf="!isLoading && !hasError && applicationsResponse && applicationsResponse.applications && applicationsResponse.applications.length === 0" class="empty-state">
            <div class="empty-content">
              <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <circle cx="9" cy="9" r="2"></circle>
                <path d="M21 15l-3.086-3.086a2 2 0 00-2.828 0L6 21"></path>
              </svg>
              <h3>No applications found</h3>
              <p>No applications match your current search and filter criteria.</p>
              <app-button variant="outline" (click)="clearFilters()">
                Clear Filters
              </app-button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="applicationsResponse && applicationsResponse.totalPages > 1" class="pagination">
            <app-button
              variant="ghost"
              size="sm"
              [disabled]="currentPage === 1"
              (click)="goToPage(currentPage - 1)"
            >
              Previous
            </app-button>

            <div class="page-numbers">
              <button
                *ngFor="let page of getPageNumbers()"
                class="page-number"
                [class.active]="page === currentPage"
                [disabled]="page === '...'"
                (click)="goToPage(page)"
              >
                {{ page }}
              </button>
            </div>

            <app-button
              variant="ghost"
              size="sm"
              [disabled]="currentPage === applicationsResponse.totalPages"
              (click)="goToPage(currentPage + 1)"
            >
              Next
            </app-button>
          </div>
        </app-card>
      </div>
    </div>
  `,
  styles: [`
    .applications-page {
      min-height: 100%;
    }

    .page-header {
      margin-bottom: var(--spacing-xl);
    }

    .header-content {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      gap: var(--spacing-lg);
    }

    .header-info {
      flex: 1;
    }

    .page-title {
      margin: 0 0 var(--spacing-xs) 0;
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);
    }

    .page-subtitle {
      margin: 0;
      font-size: var(--text-base);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .header-actions {
      display: flex;
      gap: var(--spacing-sm);
      flex-shrink: 0;
    }

    .applications-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: var(--spacing-lg);
      padding: var(--spacing-lg);
    }

    .application-item {
      padding: var(--spacing-lg);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-lg);
      background: white;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--secondary-300);
      }
    }

    .app-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-md);
    }

    .app-name {
      margin: 0;
      font-size: var(--text-lg);
      font-weight: 600;
      color: var(--secondary-900);
      flex: 1;
    }

    .app-status {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
      text-transform: uppercase;

      &.status-production {
        background: var(--success-100);
        color: var(--success-700);
      }

      &.status-development {
        background: var(--primary-100);
        color: var(--primary-700);
      }

      &.status-testing {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.status-deprecated {
        background: var(--error-100);
        color: var(--error-700);
      }
    }

    .app-description {
      margin: 0 0 var(--spacing-md) 0;
      font-size: var(--text-sm);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .app-meta {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-lg);
      font-size: var(--text-sm);
    }

    .app-owner {
      color: var(--secondary-700);
      font-weight: 500;
    }

    .app-criticality {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;

      &.criticality-critical {
        background: var(--error-100);
        color: var(--error-700);
      }

      &.criticality-high {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.criticality-medium {
        background: var(--primary-100);
        color: var(--primary-700);
      }

      &.criticality-low {
        background: var(--success-100);
        color: var(--success-700);
      }
    }

    .app-department {
      color: var(--secondary-500);
      font-size: var(--text-xs);
      margin: 0 var(--spacing-sm);
    }

    .app-scores {
      display: flex;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-md);
    }

    .score-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-xs);
    }

    .score-label {
      font-size: var(--text-xs);
      color: var(--secondary-500);
      font-weight: 500;
    }

    .score-value {
      font-size: var(--text-sm);
      font-weight: 600;
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);

      &.score-excellent {
        background: var(--success-100);
        color: var(--success-700);
      }

      &.score-good {
        background: var(--primary-100);
        color: var(--primary-700);
      }

      &.score-fair {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.score-poor {
        background: var(--error-100);
        color: var(--error-700);
      }
    }

    .app-actions {
      display: flex;
      gap: var(--spacing-sm);
    }

    /* Controls Section */
    .controls-section {
      margin-bottom: var(--spacing-lg);
      position: relative;
    }

    .search-controls {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-md);
    }

    .search-input {
      position: relative;
      flex: 1;
      max-width: 400px;
    }

    .search-icon {
      position: absolute;
      left: var(--spacing-md);
      top: 50%;
      transform: translateY(-50%);
      width: 16px;
      height: 16px;
      color: var(--secondary-400);
      z-index: 1;
    }

    .search-field {
      width: 100%;
      height: 40px;
      padding: 0 var(--spacing-md) 0 44px;
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-lg);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &::placeholder {
        color: var(--secondary-400);
      }

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .control-buttons {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
    }

    .filter-count {
      background: var(--primary-500);
      color: white;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: 600;
      margin-left: var(--spacing-xs);
    }

    .view-toggle {
      display: flex;
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      overflow: hidden;
    }

    .view-toggle app-button {
      border-radius: 0;
      border: none;
    }

    .view-toggle app-button:first-child {
      border-right: 1px solid var(--secondary-200);
    }

    .view-toggle app-button.active {
      background: var(--primary-100);
      color: var(--primary-700);
    }

    /* Filters Overlay */
    .filters-overlay {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
      z-index: 50;
      display: none;

      &.show {
        display: block;
      }
    }

    .filters-content {
      padding: var(--spacing-lg);
    }

    .filters-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid var(--secondary-200);
    }

    .filters-header h3 {
      margin: 0;
      font-size: var(--text-lg);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .close-filters {
      background: none;
      border: none;
      color: var(--secondary-400);
      cursor: pointer;
      padding: var(--spacing-xs);
      border-radius: var(--radius-sm);
      transition: color 0.2s ease;

      &:hover {
        color: var(--secondary-600);
      }

      svg {
        width: 20px;
        height: 20px;
      }
    }

    .filters-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
    }

    .filter-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .filter-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .multiselect-options {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
      max-height: 150px;
      overflow-y: auto;
      padding: var(--spacing-sm);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
    }

    .checkbox-option {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      cursor: pointer;
      padding: var(--spacing-xs);
      border-radius: var(--radius-sm);
      transition: background-color 0.2s ease;

      &:hover {
        background: var(--neutral-100);
      }
    }

    .checkbox-option input[type="checkbox"] {
      margin: 0;
    }

    .checkbox-label {
      font-size: var(--text-sm);
      color: var(--secondary-700);
      flex: 1;
    }

    .filters-actions {
      display: flex;
      justify-content: flex-end;
      gap: var(--spacing-sm);
      padding-top: var(--spacing-md);
      border-top: 1px solid var(--secondary-200);
    }

    /* Applications Table */
    .applications-table {
      padding: var(--spacing-lg);
      overflow-x: auto;
    }

    .table {
      width: 100%;
      border-collapse: collapse;
      font-size: var(--text-sm);
    }

    .table th {
      text-align: left;
      padding: var(--spacing-md);
      border-bottom: 2px solid var(--secondary-200);
      background: var(--neutral-50);
      font-weight: 600;
      color: var(--secondary-700);
      white-space: nowrap;
    }

    .table td {
      padding: var(--spacing-md);
      border-bottom: 1px solid var(--secondary-100);
      vertical-align: top;
    }

    .table-row {
      transition: background-color 0.2s ease;

      &:hover {
        background: var(--neutral-50);
      }
    }

    .app-name-cell {
      min-width: 250px;
    }

    .app-name-content .app-name {
      margin: 0 0 var(--spacing-xs) 0;
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .app-description-short {
      margin: 0;
      font-size: var(--text-xs);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .actions-cell {
      width: 140px;
    }

    .table-actions {
      display: flex;
      gap: var(--spacing-xs);
    }

    /* Loading State */
    .loading-state {
      padding: var(--spacing-lg);
    }

    .loading-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: var(--spacing-lg);
    }

    .loading-item {
      height: 200px;
      background: var(--neutral-100);
      border-radius: var(--radius-lg);
      position: relative;
      overflow: hidden;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.4),
          transparent
        );
        animation: shimmer 1.5s infinite;
      }
    }

    @keyframes shimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    /* Error State */
    .error-state {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 300px;
      padding: var(--spacing-xl);
    }

    .error-content {
      text-align: center;
      max-width: 400px;
    }

    .error-icon {
      width: 64px;
      height: 64px;
      color: var(--error-500);
      margin: 0 auto var(--spacing-lg);
    }

    .error-content h3 {
      margin-bottom: var(--spacing-sm);
      color: var(--secondary-900);
    }

    .error-content p {
      margin-bottom: var(--spacing-lg);
      color: var(--secondary-600);
    }

    /* Empty State */
    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 300px;
      padding: var(--spacing-xl);
      grid-column: 1 / -1;
    }

    .empty-content {
      text-align: center;
      max-width: 400px;
    }

    .empty-icon {
      width: 64px;
      height: 64px;
      color: var(--secondary-400);
      margin: 0 auto var(--spacing-lg);
    }

    .empty-content h3 {
      margin-bottom: var(--spacing-sm);
      color: var(--secondary-700);
    }

    .empty-content p {
      margin-bottom: var(--spacing-lg);
      color: var(--secondary-500);
    }

    /* Pagination */
    .pagination {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-md);
      padding: var(--spacing-lg);
      border-top: 1px solid var(--secondary-200);
      margin-top: var(--spacing-lg);
    }

    .page-numbers {
      display: flex;
      gap: var(--spacing-xs);
    }

    .page-number {
      padding: var(--spacing-sm) var(--spacing-md);
      border: 1px solid var(--secondary-300);
      background: white;
      color: var(--secondary-700);
      border-radius: var(--radius-md);
      cursor: pointer;
      font-size: var(--text-sm);
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background: var(--primary-50);
        border-color: var(--primary-300);
        color: var(--primary-700);
      }

      &.active {
        background: var(--primary-500);
        border-color: var(--primary-500);
        color: white;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
      }

      .header-actions {
        justify-content: flex-end;
      }

      .search-controls {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
      }

      .search-input {
        max-width: none;
      }

      .control-buttons {
        justify-content: space-between;
      }

      .applications-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .filters-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .table {
        font-size: var(--text-xs);
      }

      .table th,
      .table td {
        padding: var(--spacing-sm);
      }

      .app-name-cell {
        min-width: 200px;
      }

      .table-actions {
        flex-direction: column;
        gap: var(--spacing-xs);
      }
    }
  `]
})
export class ApplicationsListComponent implements OnInit, OnDestroy {
  // Data properties
  applicationsResponse: ApplicationsResponse | null = null;
  isLoading = true;
  hasError = false;

  // View mode
  viewMode: 'card' | 'grid' = 'card';

  // Pagination
  currentPage = 1;
  pageSize = 12;

  // Search and filtering
  searchFilterState: SearchFilterState = { search: '', filters: {} };
  filterConfigs: FilterConfig[] = [];
  showFiltersOverlay = false;

  // Reactive streams
  private destroy$ = new Subject<void>();
  private searchFilter$ = new Subject<SearchFilterState>();

  constructor(private applicationsService: ApplicationsService) {}

  ngOnInit(): void {
    this.initializeFilters();
    this.setupSearchFilterStream();
    this.loadApplications();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeFilters(): void {
    this.applicationsService.getFilterOptions()
      .pipe(takeUntil(this.destroy$))
      .subscribe(options => {
        this.filterConfigs = [
          {
            key: 'status',
            label: 'Status',
            type: 'multiselect',
            options: options.statuses.map((status: string) => ({
              value: status,
              label: status
            }))
          },
          {
            key: 'criticality',
            label: 'Criticality',
            type: 'multiselect',
            options: options.criticalities.map((criticality: string) => ({
              value: criticality,
              label: criticality
            }))
          },
          {
            key: 'owner',
            label: 'Owner',
            type: 'multiselect',
            options: options.owners.map((owner: string) => ({
              value: owner,
              label: owner
            }))
          },
          {
            key: 'department',
            label: 'Department',
            type: 'multiselect',
            options: options.departments.map((dept: string) => ({
              value: dept,
              label: dept
            }))
          }
        ];
      });
  }

  private setupSearchFilterStream(): void {
    this.searchFilter$
      .pipe(
        debounceTime(300),
        distinctUntilChanged((prev, curr) =>
          JSON.stringify(prev) === JSON.stringify(curr)
        ),
        takeUntil(this.destroy$)
      )
      .subscribe(state => {
        this.searchFilterState = state;
        this.currentPage = 1; // Reset to first page on filter change
        this.loadApplications();
      });
  }

  loadApplications(): void {
    this.isLoading = true;
    this.hasError = false;

    const filter: ApplicationsFilter = {
      search: this.searchFilterState.search,
      status: this.searchFilterState.filters['status'],
      criticality: this.searchFilterState.filters['criticality'],
      owner: this.searchFilterState.filters['owner'],
      department: this.searchFilterState.filters['department']
    };

    this.applicationsService.getApplications(this.currentPage, this.pageSize, filter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.applicationsResponse = response;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading applications:', error);
          this.hasError = true;
          this.isLoading = false;
        }
      });
  }

  onSearchFilterChange(state: SearchFilterState): void {
    this.searchFilter$.next(state);
  }

  clearFilters(): void {
    this.searchFilterState = { search: '', filters: {} };
    this.searchFilter$.next(this.searchFilterState);
  }

  goToPage(page: number | string): void {
    if (typeof page === 'number' && page !== this.currentPage) {
      this.currentPage = page;
      this.loadApplications();
    }
  }

  getPageNumbers(): (number | string)[] {
    if (!this.applicationsResponse) return [];

    const totalPages = this.applicationsResponse.totalPages;
    const current = this.currentPage;
    const pages: (number | string)[] = [];

    if (totalPages <= 7) {
      // Show all pages if 7 or fewer
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show first page
      pages.push(1);

      if (current > 4) {
        pages.push('...');
      }

      // Show pages around current
      const start = Math.max(2, current - 1);
      const end = Math.min(totalPages - 1, current + 1);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      if (current < totalPages - 3) {
        pages.push('...');
      }

      // Show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }

    return pages;
  }

  getSubtitle(): string {
    if (!this.applicationsResponse) return 'Loading...';

    const { total, applications } = this.applicationsResponse;
    const showing = applications.length;

    if (total === showing) {
      return `${total} application${total !== 1 ? 's' : ''}`;
    } else {
      return `Showing ${showing} of ${total} applications`;
    }
  }

  getScoreClass(score: number): string {
    if (score >= 90) return 'score-excellent';
    if (score >= 80) return 'score-good';
    if (score >= 70) return 'score-fair';
    return 'score-poor';
  }

  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'card' ? 'grid' : 'card';
  }

  toggleFiltersOverlay(): void {
    this.showFiltersOverlay = !this.showFiltersOverlay;
  }

  closeFiltersOverlay(): void {
    this.showFiltersOverlay = false;
  }

  getActiveFilterCount(): number {
    let count = 0;
    Object.keys(this.searchFilterState.filters).forEach(key => {
      const value = this.searchFilterState.filters[key];
      if (value && (Array.isArray(value) ? value.length > 0 : value !== '')) {
        count += Array.isArray(value) ? value.length : 1;
      }
    });
    return count;
  }

  isOptionSelected(key: string, value: string): boolean {
    const filterValue = this.searchFilterState.filters[key];
    return filterValue && Array.isArray(filterValue) && filterValue.includes(value);
  }

  onMultiSelectChange(key: string, value: string, event: any): void {
    if (!this.searchFilterState.filters[key]) {
      this.searchFilterState.filters[key] = [];
    }

    if (event.target.checked) {
      this.searchFilterState.filters[key] = [...this.searchFilterState.filters[key], value];
    } else {
      this.searchFilterState.filters[key] = this.searchFilterState.filters[key].filter((v: string) => v !== value);
    }

    this.onSearchFilterChange(this.searchFilterState);
  }

  clearFilters(): void {
    this.searchFilterState = { search: '', filters: {} };
    this.onSearchFilterChange(this.searchFilterState);
  }
}
