import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { SlideModalComponent } from '../../../../shared/components/slide-modal/slide-modal.component';
import { FormInputComponent } from '../../../../shared/components/form-input/form-input.component';

export interface DependencyFormData {
  name: string;
  type: string;
  version: string;
  criticality: string;
  isInternal: boolean;
  description: string;
}

@Component({
  selector: 'app-dependency-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],
  template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Dependency' : 'Add Dependency'"
      subtitle="Configure application dependency details"
      [loading]="loading"
      [canConfirm]="dependencyForm.valid"
      confirmText="Save Dependency"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="dependencyForm" class="dependency-form">
        <div class="form-grid">
          <app-form-input
            label="Dependency Name"
            placeholder="e.g., Angular, Express.js"
            formControlName="name"
            [required]="true"
            [errorMessage]="getFieldError('name')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Type</label>
            <select formControlName="type" class="form-select">
              <option value="library">Library</option>
              <option value="framework">Framework</option>
              <option value="service">Service</option>
              <option value="database">Database</option>
              <option value="api">API</option>
              <option value="tool">Tool</option>
              <option value="infrastructure">Infrastructure</option>
            </select>
          </div>

          <app-form-input
            label="Version"
            placeholder="e.g., 16.2.0, ^4.18.2"
            formControlName="version"
            [required]="true"
            [errorMessage]="getFieldError('version')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Criticality</label>
            <select formControlName="criticality" class="form-select">
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          <div class="form-group full-width">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                formControlName="isInternal"
                class="checkbox-input"
              >
              <span class="checkbox-text">Internal Dependency</span>
              <span class="checkbox-description">This is an internal company service or library</span>
            </label>
          </div>

          <div class="form-group full-width">
            <label class="form-label">Description</label>
            <textarea 
              formControlName="description"
              class="form-textarea"
              placeholder="Describe the purpose and usage of this dependency..."
              rows="3"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `,
  styles: [`
    .dependency-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-lg);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .form-select,
    .form-textarea {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .form-textarea {
      resize: vertical;
      min-height: 80px;
      font-family: inherit;
    }

    .checkbox-label {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-sm);
      cursor: pointer;
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--primary-300);
        background: var(--primary-25);
      }
    }

    .checkbox-input {
      margin: 0;
      margin-top: 2px;
    }

    .checkbox-text {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-900);
      margin-bottom: var(--spacing-xs);
    }

    .checkbox-description {
      font-size: var(--text-xs);
      color: var(--secondary-600);
      display: block;
    }

    @media (max-width: 768px) {
      .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }
    }
  `]
})
export class DependencyModalComponent implements OnInit {
  @Input() isOpen = false;
  @Input() editMode = false;
  @Input() initialData: DependencyFormData | null = null;
  @Input() loading = false;

  @Output() closed = new EventEmitter<void>();
  @Output() saved = new EventEmitter<DependencyFormData>();

  dependencyForm!: FormGroup;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  ngOnChanges(): void {
    if (this.dependencyForm && this.initialData) {
      this.dependencyForm.patchValue(this.initialData);
    }
  }

  private initializeForm(): void {
    this.dependencyForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      type: ['library', [Validators.required]],
      version: ['', [Validators.required]],
      criticality: ['medium', [Validators.required]],
      isInternal: [false],
      description: ['']
    });

    if (this.initialData) {
      this.dependencyForm.patchValue(this.initialData);
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.dependencyForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
    }
    return '';
  }

  onClose(): void {
    this.dependencyForm.reset();
    this.closed.emit();
  }

  onSave(): void {
    if (this.dependencyForm.valid) {
      this.saved.emit(this.dependencyForm.value);
    }
  }
}
